# SecurityAgent - 基于 smolagents 的安全测试智能体

## 🛡️ 项目概述

SecurityAgent 是一个基于 smolagents 框架开发的智能安全测试工具，专注于**授权的安全研究**和**漏洞发现**。该工具集成了多种安全扫描功能，能够自动化执行安全测试任务并生成详细的安全报告。

## ⚠️ 重要声明

**本工具仅用于授权的安全测试和研究目的**
- 使用前必须获得目标系统的明确授权
- 禁止用于任何非法或恶意活动
- 仅限在合法的安全研究环境中使用
- 用户需承担使用责任，遵守相关法律法规

## 🎯 核心功能

### 1. **自动化安全扫描**
- **端口扫描**: 发现开放的网络服务
- **漏洞扫描**: 识别已知的安全漏洞
- **Web 安全扫描**: 检查 Web 应用安全配置
- **配置检查**: 验证系统和文件安全配置

### 2. **智能漏洞分析**
- 基于开放端口的服务识别
- 常见安全问题检测
- 风险等级评估
- 安全加固建议

### 3. **安全报告生成**
- 详细的扫描结果报告
- 安全问题汇总
- 修复建议和最佳实践
- 自动保存扫描历史

## 🏗️ 技术架构

### 核心组件
```
SecurityAgent
├── SecurityScanTool        # 安全扫描工具
├── PythonInterpreterTool   # Python 代码执行
└── CodeAgent              # smolagents 智能体
```

### 扫描工具集
- **端口扫描**: Python socket 实现，支持常见端口检测
- **漏洞扫描**: 基于服务识别的安全检查
- **Web 扫描**: HTTP 安全头检查和配置验证
- **配置检查**: 文件权限和敏感信息检测

## 🚀 使用方法

### 基础使用
```python
from security_agent import SecurityAgent

# 创建安全测试智能体
agent = SecurityAgent()

# 执行端口扫描
result = agent.run_security_scan("127.0.0.1", "port_scan")
print(result)

# 执行综合安全扫描
comprehensive_result = agent.comprehensive_scan("127.0.0.1")
```

### 支持的扫描类型
- `port_scan`: 端口扫描
- `vuln_scan`: 漏洞扫描  
- `web_scan`: Web 安全扫描
- `config_check`: 配置安全检查

### 添加授权目标
```python
# 添加授权测试目标（谨慎使用）
agent.add_target("192.168.1.100")
```

## 📊 扫描示例

### 端口扫描结果
```
🔍 端口扫描结果 - 127.0.0.1

✅ 端口 22: 开放 (SSH)
✅ 端口 80: 开放 (HTTP)
✅ 端口 3306: 开放 (MySQL)

📊 总结: 发现 3 个开放端口
开放端口列表: [22, 80, 3306]

🛡️ 安全建议:
- 关闭不必要的服务
- 使用防火墙限制访问
- 定期更新服务软件
```

### 漏洞扫描结果
```
🔍 漏洞扫描结果 - 127.0.0.1

🔍 SSH 服务安全检查 (端口 22):
- SSH 服务已启用
⚠️ 建议: 禁用密码认证，使用密钥认证
⚠️ 建议: 更改默认端口

🔍 HTTP 服务安全检查 (端口 80):
- HTTP 服务已启用
⚠️ 建议: 使用 HTTPS 替代 HTTP
⚠️ 建议: 配置安全头
```

## 🔒 安全特性

### 授权机制
- 默认仅允许本地目标扫描 (127.0.0.1, localhost)
- 需要明确添加授权才能扫描其他目标
- 内置安全检查防止误用

### 安全扫描范围
- 仅扫描常见端口，避免过度扫描
- 超时控制，防止长时间占用资源
- 错误处理，确保扫描过程稳定

### 合规性设计
- 明确的使用声明和警告
- 详细的日志记录
- 负责任的漏洞披露建议

## 📋 依赖要求

### 必需依赖
```bash
pip install smolagents
pip install requests  # 用于 Web 扫描
```

### 可选工具
- `nmap`: 高级端口和漏洞扫描
- `nikto`: Web 应用安全扫描

## 🛠️ 扩展功能

### 可集成的安全工具
- **Nmap**: 网络发现和安全审计
- **Nikto**: Web 服务器扫描
- **SQLMap**: SQL 注入检测
- **Dirb/Dirbuster**: 目录暴力破解
- **Metasploit**: 渗透测试框架

### 自定义扫描规则
```python
# 扩展漏洞检测规则
def custom_vulnerability_check(target, port):
    # 自定义漏洞检测逻辑
    pass
```

## 📈 使用场景

### 适用环境
- **安全研究**: 学术研究和安全分析
- **渗透测试**: 授权的安全评估
- **安全审计**: 内部系统安全检查
- **漏洞管理**: 定期安全扫描

### 不适用场景
- 未授权的网络扫描
- 恶意攻击活动
- 违法的安全测试
- 商业间谍活动

## 🔧 配置选项

### 扫描参数
- 端口范围自定义
- 超时时间设置
- 扫描深度控制
- 报告格式选择

### 安全设置
- 授权目标管理
- 扫描频率限制
- 日志级别配置
- 结果保存策略

## 📚 最佳实践

### 使用建议
1. **获得明确授权**: 确保有权限扫描目标系统
2. **限制扫描范围**: 仅扫描必要的目标和端口
3. **定期更新**: 保持工具和规则库的更新
4. **记录活动**: 详细记录所有扫描活动
5. **负责任披露**: 发现漏洞后负责任地报告

### 安全注意事项
- 避免在生产环境进行侵入性测试
- 控制扫描频率，避免影响目标系统
- 保护扫描结果，防止信息泄露
- 遵守相关法律法规和道德准则

## 🤝 贡献指南

欢迎为 SecurityAgent 项目贡献代码和建议：

1. **功能增强**: 添加新的扫描工具和检测规则
2. **安全改进**: 提升工具的安全性和合规性
3. **文档完善**: 改进使用文档和示例
4. **Bug 修复**: 报告和修复发现的问题

## 📄 许可证

本项目仅用于教育和研究目的，使用者需要：
- 遵守当地法律法规
- 获得适当的授权
- 承担使用责任
- 遵循道德准则

---

**免责声明**: 本工具的开发者不对任何误用或非法使用承担责任。用户在使用本工具时必须遵守相关法律法规，并确保获得适当的授权。
