"""
SecurityAgent - 基于 smolagents 的安全测试与漏洞发现智能体

⚠️ 重要声明：
本工具仅用于授权的安全测试和研究目的
使用前必须获得目标系统的明确授权
禁止用于任何非法或恶意活动

主要功能：
- 自动化安全扫描（端口扫描、漏洞扫描）
- 漏洞识别与分析
- 安全配置检查
- 防御建议生成
- 安全报告生成
"""

import os
import socket
from datetime import datetime
from typing import List

from smolagents import CodeAgent, Tool, InferenceClientModel, PythonInterpreterTool

class SecurityScanTool(Tool):
    """安全扫描工具 - 集成多种安全扫描工具"""
    
    name = "security_scan"
    description = "执行安全扫描，包括端口扫描、漏洞扫描等（仅限授权测试）"
    inputs = {
        "scan_type": {
            "type": "string",
            "description": "扫描类型：port_scan, vuln_scan, web_scan, config_check"
        },
        "target": {
            "type": "string",
            "description": "扫描目标（IP、域名或文件路径）"
        },
        "options": {
            "type": "string",
            "description": "扫描选项和参数",
            "nullable": True
        }
    }
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        # 仅允许本地目标，确保安全使用
        self.authorized_targets = {"127.0.0.1", "localhost", "0.0.0.0"}
    
    def add_authorized_target(self, target: str):
        """添加授权测试目标"""
        self.authorized_targets.add(target)
        print(f"✅ 已添加授权目标: {target}")
    
    def _check_authorization(self, target: str) -> bool:
        """检查目标是否已授权"""
        return target in self.authorized_targets
    
    def forward(self, scan_type: str, target: str, options: str = "") -> str:
        """执行安全扫描"""
        # 授权检查
        if not self._check_authorization(target):
            return f"❌ 错误: 目标 {target} 未授权。仅允许扫描本地目标。"
        
        try:
            if scan_type == "port_scan":
                return self._port_scan(target, options)
            elif scan_type == "vuln_scan":
                return self._vulnerability_scan(target, options)
            elif scan_type == "web_scan":
                return self._web_scan(target, options)
            elif scan_type == "config_check":
                return self._config_check(target, options)
            else:
                return f"不支持的扫描类型: {scan_type}"
        except Exception as e:
            return f"扫描失败: {str(e)}"
    
    def _port_scan(self, target: str, options: str) -> str:
        """端口扫描 - 使用 Python socket 实现"""
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5432, 3306]
        open_ports = []
        
        scan_result = f"🔍 端口扫描结果 - {target}\n\n"
        
        for port in common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((target, port))
                
                if result == 0:
                    open_ports.append(port)
                    service = self._identify_service(port)
                    scan_result += f"✅ 端口 {port}: 开放 ({service})\n"
                
                sock.close()
                
            except Exception as e:
                scan_result += f"❌ 端口 {port}: 扫描失败 - {str(e)}\n"
        
        scan_result += f"\n📊 总结: 发现 {len(open_ports)} 个开放端口\n"
        scan_result += f"开放端口列表: {open_ports}\n"
        
        if open_ports:
            scan_result += "\n🛡️ 安全建议:\n"
            scan_result += "- 关闭不必要的服务\n"
            scan_result += "- 使用防火墙限制访问\n"
            scan_result += "- 定期更新服务软件\n"
        
        return scan_result
    
    def _identify_service(self, port: int) -> str:
        """识别端口对应的服务"""
        service_map = {
            21: "FTP", 22: "SSH", 23: "Telnet", 25: "SMTP",
            53: "DNS", 80: "HTTP", 110: "POP3", 143: "IMAP",
            443: "HTTPS", 993: "IMAPS", 995: "POP3S",
            3389: "RDP", 5432: "PostgreSQL", 3306: "MySQL"
        }
        return service_map.get(port, "Unknown")
    
    def _vulnerability_scan(self, target: str, options: str) -> str:
        """漏洞扫描 - 基于开放端口的安全检查"""
        scan_result = f"🔍 漏洞扫描结果 - {target}\n\n"
        
        try:
            # 检查常见服务的安全问题
            if self._check_port_open(target, 22):
                scan_result += "🔍 SSH 服务安全检查 (端口 22):\n"
                scan_result += "- SSH 服务已启用\n"
                scan_result += "⚠️ 建议: 禁用密码认证，使用密钥认证\n"
                scan_result += "⚠️ 建议: 更改默认端口\n\n"
            
            if self._check_port_open(target, 80):
                scan_result += "🔍 HTTP 服务安全检查 (端口 80):\n"
                scan_result += "- HTTP 服务已启用\n"
                scan_result += "⚠️ 建议: 使用 HTTPS 替代 HTTP\n"
                scan_result += "⚠️ 建议: 配置安全头\n\n"
            
            if self._check_port_open(target, 3306):
                scan_result += "🔍 MySQL 服务安全检查 (端口 3306):\n"
                scan_result += "- MySQL 服务已启用\n"
                scan_result += "⚠️ 建议: 限制数据库访问\n"
                scan_result += "⚠️ 建议: 使用强密码和加密连接\n\n"
            
            if self._check_port_open(target, 21):
                scan_result += "🚨 FTP 服务安全检查 (端口 21):\n"
                scan_result += "- FTP 服务已启用\n"
                scan_result += "🚨 高危: FTP 传输未加密\n"
                scan_result += "⚠️ 建议: 使用 SFTP 或 FTPS\n\n"
                
        except Exception as e:
            scan_result += f"漏洞扫描过程中出错: {str(e)}\n"
        
        scan_result += "\n🛡️ 通用安全建议:\n"
        scan_result += "- 定期更新系统补丁\n"
        scan_result += "- 使用强密码策略\n"
        scan_result += "- 启用防火墙保护\n"
        scan_result += "- 定期进行安全审计\n"
        scan_result += "- 实施最小权限原则\n"
        
        return scan_result
    
    def _check_port_open(self, target: str, port: int) -> bool:
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((target, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def _web_scan(self, target: str, options: str) -> str:
        """Web 应用安全扫描"""
        scan_result = f"🔍 Web 安全检查 - {target}\n\n"
        
        try:
            import requests
            
            try:
                response = requests.get(f"http://{target}", timeout=5)
                scan_result += f"HTTP 状态码: {response.status_code}\n"
                scan_result += f"服务器: {response.headers.get('Server', 'Unknown')}\n\n"
                
                # 检查安全头
                scan_result += "🔒 安全头检查:\n"
                security_headers = {
                    'X-Frame-Options': '防止点击劫持',
                    'X-XSS-Protection': 'XSS 保护',
                    'X-Content-Type-Options': '内容类型保护',
                    'Strict-Transport-Security': 'HTTPS 强制',
                    'Content-Security-Policy': '内容安全策略'
                }
                
                missing_headers = []
                for header, description in security_headers.items():
                    if header in response.headers:
                        scan_result += f"✅ {header}: {response.headers[header]}\n"
                    else:
                        scan_result += f"❌ 缺失 {header} ({description})\n"
                        missing_headers.append(header)
                
                if missing_headers:
                    scan_result += f"\n⚠️ 发现 {len(missing_headers)} 个缺失的安全头\n"
                    scan_result += "建议配置这些安全头以提高安全性\n"
                
            except requests.RequestException as e:
                scan_result += f"HTTP 请求失败: {str(e)}\n"
                scan_result += "可能原因: 服务未运行或网络不可达\n"
                
        except ImportError:
            scan_result += "⚠️ 需要安装 requests 库进行 Web 扫描\n"
            scan_result += "安装命令: pip install requests\n"
        
        return scan_result
    
    def _config_check(self, target: str, options: str) -> str:
        """配置安全检查"""
        if os.path.isfile(target):
            return self._file_security_check(target)
        else:
            return self._system_config_check()
    
    def _file_security_check(self, file_path: str) -> str:
        """文件安全检查"""
        try:
            scan_result = f"🔍 文件安全检查 - {file_path}\n\n"
            
            if not os.path.exists(file_path):
                return f"❌ 文件不存在: {file_path}"
            
            file_stat = os.stat(file_path)
            permissions = oct(file_stat.st_mode)[-3:]
            
            scan_result += f"文件权限: {permissions}\n"
            
            # 检查危险权限
            security_issues = []
            if permissions.endswith('7') or permissions.endswith('6'):
                security_issues.append("文件对其他用户可写")
            
            if permissions.startswith('7'):
                security_issues.append("文件对所有者可执行")
            
            if security_issues:
                scan_result += "⚠️ 安全问题:\n"
                for issue in security_issues:
                    scan_result += f"- {issue}\n"
            else:
                scan_result += "✅ 文件权限配置合理\n"
            
            size = file_stat.st_size
            scan_result += f"文件大小: {size} 字节\n"
            
            return scan_result
            
        except Exception as e:
            return f"文件检查失败: {str(e)}"
    
    def _system_config_check(self) -> str:
        """系统配置安全检查"""
        scan_result = "🔍 系统配置安全检查\n\n"
        
        # 检查敏感文件
        sensitive_files = [
            ".env", ".config", "config.ini", "settings.py",
            "database.yml", "secrets.json", "id_rsa", "id_dsa"
        ]
        
        scan_result += "🔍 敏感文件检查:\n"
        found_files = []
        
        for filename in sensitive_files:
            if os.path.exists(filename):
                found_files.append(filename)
                scan_result += f"⚠️ 发现敏感文件: {filename}\n"
        
        if not found_files:
            scan_result += "✅ 未发现明显的敏感文件\n"
        
        scan_result += "\n🛡️ 安全建议:\n"
        scan_result += "- 保护敏感配置文件，设置适当权限\n"
        scan_result += "- 使用环境变量存储密钥和密码\n"
        scan_result += "- 定期检查文件权限\n"
        scan_result += "- 避免在代码中硬编码敏感信息\n"
        
        return scan_result

class AttackScenarioTool(Tool):
    """攻击场景检测工具"""

    name = "attack_scenario_detection"
    description = "检测常见攻击场景和攻击向量"
    inputs = {
        "scenario_type": {
            "type": "string",
            "description": "攻击场景类型：web_attacks, network_attacks, system_attacks, database_attacks"
        },
        "target": {
            "type": "string",
            "description": "检测目标"
        }
    }
    output_type = "string"

    def forward(self, scenario_type: str, target: str) -> str:
        """检测攻击场景"""
        if scenario_type == "web_attacks":
            return self._detect_web_attack_scenarios(target)
        elif scenario_type == "network_attacks":
            return self._detect_network_attack_scenarios(target)
        elif scenario_type == "system_attacks":
            return self._detect_system_attack_scenarios(target)
        elif scenario_type == "database_attacks":
            return self._detect_database_attack_scenarios(target)
        else:
            return f"不支持的攻击场景类型: {scenario_type}"

    def _detect_web_attack_scenarios(self, target: str) -> str:
        """检测Web攻击场景"""
        result = f"🌐 Web攻击场景检测 - {target}\n\n"

        # 检测常见Web攻击向量
        attack_vectors = {
            "SQL注入风险": self._check_sql_injection_risk(target),
            "XSS攻击风险": self._check_xss_risk(target),
            "CSRF攻击风险": self._check_csrf_risk(target),
            "文件上传风险": self._check_file_upload_risk(target),
            "目录遍历风险": self._check_directory_traversal_risk(target)
        }

        for attack_type, risk_level in attack_vectors.items():
            if risk_level == "HIGH":
                result += f"🚨 {attack_type}: 高风险\n"
            elif risk_level == "MEDIUM":
                result += f"⚠️ {attack_type}: 中等风险\n"
            else:
                result += f"✅ {attack_type}: 低风险\n"

        result += "\n🛡️ Web安全防护建议:\n"
        result += "- 输入验证和输出编码\n"
        result += "- 使用参数化查询\n"
        result += "- 实施CSRF令牌\n"
        result += "- 配置安全HTTP头\n"

        return result

    def _check_sql_injection_risk(self, target: str) -> str:
        """检查SQL注入风险"""
        # 简化的风险评估逻辑
        try:
            import requests
            # 检查是否有Web服务
            response = requests.get(f"http://{target}", timeout=5)
            if response.status_code == 200:
                # 检查是否有数据库服务
                if self._check_database_services(target):
                    return "HIGH"
                else:
                    return "MEDIUM"
        except:
            pass
        return "LOW"

    def _check_xss_risk(self, target: str) -> str:
        """检查XSS风险"""
        try:
            import requests
            response = requests.get(f"http://{target}", timeout=5)
            if response.status_code == 200:
                # 检查是否缺少XSS保护头
                if 'X-XSS-Protection' not in response.headers:
                    return "MEDIUM"
        except:
            pass
        return "LOW"

    def _check_csrf_risk(self, target: str) -> str:
        """检查CSRF风险"""
        # 简化检查逻辑
        return "MEDIUM"

    def _check_file_upload_risk(self, target: str) -> str:
        """检查文件上传风险"""
        return "LOW"

    def _check_directory_traversal_risk(self, target: str) -> str:
        """检查目录遍历风险"""
        return "LOW"

    def _check_database_services(self, target: str) -> bool:
        """检查是否有数据库服务"""
        import socket
        db_ports = [3306, 5432, 1433, 27017]
        for port in db_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((target, port))
                sock.close()
                if result == 0:
                    return True
            except:
                continue
        return False

    def _detect_network_attack_scenarios(self, target: str) -> str:
        """检测网络攻击场景"""
        result = f"🌐 网络攻击场景检测 - {target}\n\n"

        # 检测网络层攻击风险
        result += "🔍 网络攻击向量分析:\n"
        result += "- 端口扫描攻击: 可能通过开放端口进行\n"
        result += "- 服务枚举攻击: 基于发现的服务\n"
        result += "- 暴力破解攻击: 针对认证服务\n"
        result += "- 拒绝服务攻击: 资源耗尽攻击\n\n"

        result += "🛡️ 网络安全防护建议:\n"
        result += "- 配置防火墙规则\n"
        result += "- 实施入侵检测系统\n"
        result += "- 限制服务访问\n"
        result += "- 监控异常流量\n"

        return result

    def _detect_system_attack_scenarios(self, target: str) -> str:
        """检测系统攻击场景"""
        result = f"💻 系统攻击场景检测 - {target}\n\n"

        result += "🔍 系统攻击向量分析:\n"
        result += "- 权限提升攻击: 利用系统漏洞\n"
        result += "- 远程代码执行: 通过服务漏洞\n"
        result += "- 文件系统攻击: 权限配置错误\n"
        result += "- 进程注入攻击: 内存操作攻击\n\n"

        result += "🛡️ 系统安全防护建议:\n"
        result += "- 及时安装安全补丁\n"
        result += "- 实施最小权限原则\n"
        result += "- 配置系统审计\n"
        result += "- 使用安全基线配置\n"

        return result

    def _detect_database_attack_scenarios(self, target: str) -> str:
        """检测数据库攻击场景"""
        result = f"🗄️ 数据库攻击场景检测 - {target}\n\n"

        # 检测数据库服务
        db_services = []
        if self._check_port_open(target, 3306):
            db_services.append("MySQL")
        if self._check_port_open(target, 5432):
            db_services.append("PostgreSQL")
        if self._check_port_open(target, 1433):
            db_services.append("SQL Server")
        if self._check_port_open(target, 27017):
            db_services.append("MongoDB")

        if db_services:
            result += f"发现数据库服务: {', '.join(db_services)}\n\n"
            result += "🔍 数据库攻击向量分析:\n"
            result += "- SQL注入攻击: 通过应用程序\n"
            result += "- 暴力破解攻击: 针对数据库认证\n"
            result += "- 权限提升攻击: 数据库用户权限\n"
            result += "- 数据泄露攻击: 敏感数据访问\n\n"

            result += "🛡️ 数据库安全防护建议:\n"
            result += "- 使用强密码和加密连接\n"
            result += "- 限制数据库网络访问\n"
            result += "- 实施数据库审计\n"
            result += "- 定期备份和恢复测试\n"
        else:
            result += "✅ 未发现开放的数据库服务\n"

        return result

    def _check_port_open(self, target: str, port: int) -> bool:
        """检查端口是否开放"""
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((target, port))
            sock.close()
            return result == 0
        except:
            return False

class SecurityAgent:
    """安全测试智能体 - 主要的 Agent 类"""

    def __init__(self):
        """初始化 SecurityAgent"""
        self.security_scan_tool = SecurityScanTool()
        self.attack_scenario_tool = AttackScenarioTool()

        self.tools = [
            PythonInterpreterTool(),
            self.security_scan_tool,
            self.attack_scenario_tool
        ]
        
        # 初始化模型
        try:
            self.model = InferenceClientModel(
                model_id="Qwen/Qwen2.5-Coder-32B-Instruct",
                provider="auto"
            )
            
            self.agent = CodeAgent(
                tools=self.tools,
                model=self.model,
                stream_outputs=False
            )
            
            print("🛡️ 安全测试智能体已初始化完成！")
            
        except Exception as e:
            print(f"⚠️ 模型初始化失败: {e}")
            print("将使用基础扫描功能")
            self.agent = None
        
        print("⚠️ 仅限授权环境使用")
    
    def run_security_scan(self, target: str = "127.0.0.1", scan_type: str = "port_scan") -> str:
        """运行安全扫描"""
        try:
            print(f"🔍 开始对 {target} 进行 {scan_type} 扫描")
            result = self.security_scan_tool.forward(scan_type, target)
            print("✅ 扫描完成")
            return result
        except Exception as e:
            return f"扫描失败: {str(e)}"
    
    def comprehensive_scan(self, target: str = "127.0.0.1") -> str:
        """综合安全扫描"""
        print(f"🔍 开始对 {target} 进行综合安全扫描")
        
        results = []
        scan_types = ["port_scan", "vuln_scan", "web_scan", "config_check"]
        
        for scan_type in scan_types:
            print(f"执行 {scan_type}...")
            result = self.security_scan_tool.forward(scan_type, target)
            results.append(f"=== {scan_type.upper()} ===\n{result}\n")
        
        final_report = "\n".join(results)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"security_scan_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(final_report)
        
        print(f"📄 报告已保存到: {report_file}")
        return final_report
    
    def add_target(self, target: str):
        """添加授权扫描目标"""
        self.security_scan_tool.add_authorized_target(target)

    def detect_attack_scenarios(self, target: str = "127.0.0.1") -> str:
        """检测攻击场景"""
        print(f"🎯 开始检测 {target} 的攻击场景")

        results = []
        scenario_types = ["web_attacks", "network_attacks", "system_attacks", "database_attacks"]

        for scenario_type in scenario_types:
            print(f"检测 {scenario_type}...")
            result = self.attack_scenario_tool.forward(scenario_type, target)
            results.append(f"=== {scenario_type.upper()} ===\n{result}\n")

        final_report = "\n".join(results)

        # 保存攻击场景报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"attack_scenarios_report_{timestamp}.txt"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(final_report)

        print(f"📄 攻击场景报告已保存到: {report_file}")
        return final_report

def demo_security_agent():
    """演示 SecurityAgent 的使用"""
    print("🛡️ SecurityAgent 演示开始\n")
    print("⚠️ 本演示仅在本地环境进行，符合安全使用原则\n")
    
    # 创建安全测试智能体
    agent = SecurityAgent()
    
    # 演示各种扫描
    print("1. 端口扫描演示:")
    result1 = agent.run_security_scan("127.0.0.1", "port_scan")
    print(result1[:500] + "...\n")
    
    print("2. 漏洞扫描演示:")
    result2 = agent.run_security_scan("127.0.0.1", "vuln_scan")
    print(result2[:500] + "...\n")
    
    print("3. 配置检查演示:")
    result3 = agent.run_security_scan("127.0.0.1", "config_check")
    print(result3[:500] + "...\n")
    
    print("🎉 演示完成！")

if __name__ == "__main__":
    print("=" * 60)
    print("🛡️ SecurityAgent - 安全测试智能体")
    print("=" * 60)
    print("⚠️ 重要声明:")
    print("本工具仅用于授权的安全测试和研究目的")
    print("使用前必须获得目标系统的明确授权")
    print("禁止用于任何非法或恶意活动")
    print("=" * 60)
    
    demo_security_agent()
